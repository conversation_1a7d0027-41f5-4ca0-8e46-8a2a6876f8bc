#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FVG水平线功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加PySide6目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'PySide6'))

from support_and_resistance import SupportResistanceAnalyzer

def create_test_data():
    """创建测试数据"""
    # 创建50根K线的测试数据
    dates = pd.date_range(start='2024-01-01', periods=50, freq='15min')
    
    # 创建一些有FVG的价格数据
    np.random.seed(42)
    base_price = 100.0
    prices = []
    
    for i in range(50):
        # 创建一些有明显FVG的价格模式
        if i == 10:  # 创建看涨FVG
            high = base_price + 2
            low = base_price - 1
            open_price = base_price
            close = base_price + 1.5
        elif i == 11:  # 中间K线
            high = base_price + 0.5
            low = base_price - 0.5
            open_price = base_price + 1.5
            close = base_price
        elif i == 12:  # 完成看涨FVG
            high = base_price + 4
            low = base_price + 2.5  # 这里形成FVG
            open_price = base_price
            close = base_price + 3
        elif i == 25:  # 创建看跌FVG
            high = base_price + 1
            low = base_price - 2
            open_price = base_price
            close = base_price - 1.5
        elif i == 26:  # 中间K线
            high = base_price + 0.5
            low = base_price - 0.5
            open_price = base_price - 1.5
            close = base_price
        elif i == 27:  # 完成看跌FVG
            high = base_price - 2.5  # 这里形成FVG
            low = base_price - 4
            open_price = base_price
            close = base_price - 3
        else:
            # 正常的随机价格
            volatility = 0.5
            change = np.random.normal(0, volatility)
            high = base_price + abs(change) + 0.2
            low = base_price - abs(change) - 0.2
            open_price = base_price
            close = base_price + change
        
        prices.append({
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': np.random.randint(1000, 10000)
        })
        
        base_price = close  # 下一根K线的基准价格
    
    df = pd.DataFrame(prices, index=dates)
    return df

def test_fvg_horizontal_lines():
    """测试FVG水平线功能"""
    print("=== 测试FVG水平线功能 ===")
    
    # 创建测试数据
    df = create_test_data()
    print(f"创建了 {len(df)} 根测试K线")
    
    # 创建分析器
    analyzer = SupportResistanceAnalyzer(swing_length=5, fvg_range_limit=20)
    
    # 进行分析
    print("\n正在进行支撑阻力位分析...")
    result = analyzer.analyze_support_resistance(
        df, 
        enable_fvg_filter=True, 
        enable_rectangle_zones=True,
        enable_fvg_horizontal_lines=True
    )
    
    # 显示结果
    print(f"\n=== 分析结果 ===")
    print(f"Swing点: {result['swing_points']['total_swing_highs']}个阻力点, {result['swing_points']['total_swing_lows']}个支撑点")
    print(f"传统水平线: {result['horizontal_lines']['total_resistance_lines']}条阻力线, {result['horizontal_lines']['total_support_lines']}条支撑线")
    print(f"FVG水平线: {result['fvg_horizontal_lines']['total_fvg_resistance_lines']}条FVG阻力线, {result['fvg_horizontal_lines']['total_fvg_support_lines']}条FVG支撑线")
    print(f"矩形区域: {result['rectangle_zones']['total_resistance_zones']}个阻力区域, {result['rectangle_zones']['total_support_zones']}个支撑区域")
    print(f"FVG区域: {result['fvg_regions']['total_fvgs']}个")
    
    # 显示FVG水平线详情
    if result['fvg_horizontal_lines']['fvg_resistance_lines']:
        print(f"\n=== FVG阻力线详情 ===")
        for i, line in enumerate(result['fvg_horizontal_lines']['fvg_resistance_lines'], 1):
            print(f"  {i}. 价格: {line['price']:.2f}, 开始时间: {line['start_time']}, FVG类型: {line['fvg_type']}")
    
    if result['fvg_horizontal_lines']['fvg_support_lines']:
        print(f"\n=== FVG支撑线详情 ===")
        for i, line in enumerate(result['fvg_horizontal_lines']['fvg_support_lines'], 1):
            print(f"  {i}. 价格: {line['price']:.2f}, 开始时间: {line['start_time']}, FVG类型: {line['fvg_type']}")
    
    # 测试当前支撑阻力位功能
    print(f"\n=== 当前支撑阻力位分析 ===")
    current_levels = analyzer.get_current_support_resistance_levels(df)
    current_price = current_levels['current_price']
    print(f"当前价格: {current_price:.2f}")
    
    print(f"\n最近的阻力位:")
    for i, resistance in enumerate(current_levels['nearest_resistance'][:5], 1):
        print(f"  {i}. {resistance['price']:.2f} (距离: +{resistance['distance_percent']:.2f}%) - {resistance['type']}")
    
    print(f"\n最近的支撑位:")
    for i, support in enumerate(current_levels['nearest_support'][:5], 1):
        print(f"  {i}. {support['price']:.2f} (距离: -{support['distance_percent']:.2f}%) - {support['type']}")
    
    return result

if __name__ == "__main__":
    try:
        result = test_fvg_horizontal_lines()
        print(f"\n=== 测试完成 ===")
        print("FVG水平线功能测试成功！")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
