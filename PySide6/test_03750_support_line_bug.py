#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试03750股票60分钟支撑线突破bug的修复效果
"""

import pandas as pd
import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from support_and_resistance import analyze_kline_data, create_analyzer

def create_test_data_for_03750():
    """
    创建模拟03750股票的测试数据
    包含7月24日11:30的支撑线和7月25日15:30的突破
    """
    # 创建测试数据 - 模拟60分钟K线，确保有足够数据让7月24日11:30成为swing点
    test_data = [
        # 更早期的数据，确保有足够的历史数据
        {'time': '2024-07-15 10:30', 'open': 11.00, 'high': 11.20, 'low': 10.90, 'close': 11.10, 'volume': 1000000},
        {'time': '2024-07-15 11:30', 'open': 11.10, 'high': 11.30, 'low': 11.00, 'close': 11.25, 'volume': 1100000},
        {'time': '2024-07-15 14:00', 'open': 11.25, 'high': 11.40, 'low': 11.15, 'close': 11.35, 'volume': 1200000},
        {'time': '2024-07-15 15:00', 'open': 11.35, 'high': 11.50, 'low': 11.25, 'close': 11.45, 'volume': 1300000},

        {'time': '2024-07-16 10:30', 'open': 11.45, 'high': 11.60, 'low': 11.35, 'close': 11.55, 'volume': 1400000},
        {'time': '2024-07-16 11:30', 'open': 11.55, 'high': 11.70, 'low': 11.45, 'close': 11.65, 'volume': 1500000},
        {'time': '2024-07-16 14:00', 'open': 11.65, 'high': 11.80, 'low': 11.55, 'close': 11.75, 'volume': 1600000},
        {'time': '2024-07-16 15:00', 'open': 11.75, 'high': 11.90, 'low': 11.65, 'close': 11.85, 'volume': 1700000},

        {'time': '2024-07-17 10:30', 'open': 11.85, 'high': 12.00, 'low': 11.75, 'close': 11.95, 'volume': 1800000},
        {'time': '2024-07-17 11:30', 'open': 11.95, 'high': 12.10, 'low': 11.85, 'close': 12.05, 'volume': 1900000},
        {'time': '2024-07-17 14:00', 'open': 12.05, 'high': 12.20, 'low': 11.95, 'close': 12.15, 'volume': 2000000},
        {'time': '2024-07-17 15:00', 'open': 12.15, 'high': 12.30, 'low': 12.05, 'close': 12.25, 'volume': 2100000},

        {'time': '2024-07-18 10:30', 'open': 12.25, 'high': 12.40, 'low': 12.15, 'close': 12.35, 'volume': 2200000},
        {'time': '2024-07-18 11:30', 'open': 12.35, 'high': 12.50, 'low': 12.25, 'close': 12.45, 'volume': 2300000},
        {'time': '2024-07-18 14:00', 'open': 12.45, 'high': 12.60, 'low': 12.35, 'close': 12.55, 'volume': 2400000},
        {'time': '2024-07-18 15:00', 'open': 12.55, 'high': 12.70, 'low': 12.45, 'close': 12.65, 'volume': 2500000},

        # 开始下跌趋势，为7月24日11:30的swing low做准备
        {'time': '2024-07-19 10:30', 'open': 12.65, 'high': 12.70, 'low': 12.40, 'close': 12.50, 'volume': 2600000},
        {'time': '2024-07-19 11:30', 'open': 12.50, 'high': 12.55, 'low': 12.20, 'close': 12.30, 'volume': 2700000},
        {'time': '2024-07-19 14:00', 'open': 12.30, 'high': 12.35, 'low': 12.00, 'close': 12.10, 'volume': 2800000},
        {'time': '2024-07-19 15:00', 'open': 12.10, 'high': 12.15, 'low': 11.80, 'close': 11.90, 'volume': 2900000},

        {'time': '2024-07-22 10:30', 'open': 11.90, 'high': 11.95, 'low': 11.60, 'close': 11.70, 'volume': 3000000},
        {'time': '2024-07-22 11:30', 'open': 11.70, 'high': 11.75, 'low': 11.40, 'close': 11.50, 'volume': 3100000},
        {'time': '2024-07-22 14:00', 'open': 11.50, 'high': 11.55, 'low': 11.20, 'close': 11.30, 'volume': 3200000},
        {'time': '2024-07-22 15:00', 'open': 11.30, 'high': 11.35, 'low': 11.00, 'close': 11.10, 'volume': 3300000},

        {'time': '2024-07-23 10:30', 'open': 11.10, 'high': 11.15, 'low': 10.90, 'close': 11.00, 'volume': 3400000},
        {'time': '2024-07-23 11:30', 'open': 11.00, 'high': 11.05, 'low': 10.85, 'close': 10.95, 'volume': 3500000},
        {'time': '2024-07-23 14:00', 'open': 10.95, 'high': 11.00, 'low': 10.80, 'close': 10.90, 'volume': 3600000},
        {'time': '2024-07-23 15:00', 'open': 10.90, 'high': 10.95, 'low': 10.75, 'close': 10.85, 'volume': 3700000},

        # 7月24日11:30 - 这根K线应该形成swing支撑点（最低点）
        {'time': '2024-07-24 10:30', 'open': 10.85, 'high': 10.90, 'low': 10.70, 'close': 10.80, 'volume': 3800000},
        {'time': '2024-07-24 11:30', 'open': 10.80, 'high': 10.85, 'low': 10.60, 'close': 10.70, 'volume': 4000000},  # 关键支撑点 - 最低价10.60
        {'time': '2024-07-24 14:00', 'open': 10.70, 'high': 10.90, 'low': 10.65, 'close': 10.85, 'volume': 3900000},
        {'time': '2024-07-24 15:00', 'open': 10.85, 'high': 11.00, 'low': 10.80, 'close': 10.95, 'volume': 3800000},

        # 反弹阶段
        {'time': '2024-07-25 10:30', 'open': 10.95, 'high': 11.10, 'low': 10.90, 'close': 11.05, 'volume': 3700000},
        {'time': '2024-07-25 11:30', 'open': 11.05, 'high': 11.20, 'low': 11.00, 'close': 11.15, 'volume': 3600000},
        {'time': '2024-07-25 14:00', 'open': 11.15, 'high': 11.30, 'low': 11.10, 'close': 11.25, 'volume': 3500000},

        # 7月25日15:00 - 这根K线应该突破支撑线
        {'time': '2024-07-25 15:00', 'open': 11.25, 'high': 11.30, 'low': 10.50, 'close': 10.55, 'volume': 5000000},  # 突破 - 最低价10.50 < 支撑价10.60
    ]
    
    # 转换为DataFrame
    df = pd.DataFrame(test_data)
    
    # 转换列名为支撑阻力算法需要的格式
    df = df.rename(columns={
        'open': 'Open',
        'high': 'High', 
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    })
    
    # 设置时间索引
    df['datetime'] = pd.to_datetime(df['time'])
    df = df.set_index('datetime')
    
    return df

def test_support_line_breakthrough():
    """测试支撑线突破检测"""
    print("=" * 60)
    print("🧪 测试03750股票60分钟支撑线突破bug修复")
    print("=" * 60)
    
    # 创建测试数据
    test_df = create_test_data_for_03750()
    print(f"📊 测试数据创建完成: {len(test_df)}条60分钟K线")
    print(f"📅 时间范围: {test_df.index[0]} 到 {test_df.index[-1]}")
    
    # 创建分析器
    analyzer = create_analyzer(swing_length=8, fvg_range_limit=100)
    
    # 测试1: 分析到7月24日11:30为止的数据（应该有支撑线）
    print("\n" + "=" * 50)
    print("🔍 测试1: 分析到7月24日11:30的数据（应该检测到支撑线）")
    print("=" * 50)
    
    # 截取到7月24日15:00的数据
    cutoff_time = pd.to_datetime('2024-07-24 15:00')
    df_before_breakthrough = test_df[test_df.index <= cutoff_time].copy()
    
    print(f"📊 分析数据: {len(df_before_breakthrough)}条K线")
    result_before = analyzer.analyze_support_resistance(df_before_breakthrough)
    
    if result_before and 'horizontal_lines' in result_before:
        support_lines = result_before['horizontal_lines']['support_lines']
        print(f"✅ 检测到{len(support_lines)}条支撑线:")
        for i, line in enumerate(support_lines):
            print(f"   支撑线{i+1}: 价格={line['price']:.3f}, 时间={line['start_time']}")
    else:
        print("❌ 未检测到支撑线")
    
    # 测试2: 分析包含7月25日15:30突破数据（支撑线应该被过滤掉）
    print("\n" + "=" * 50)
    print("🔍 测试2: 分析包含7月25日15:00突破的完整数据（支撑线应该被过滤）")
    print("=" * 50)
    
    print(f"📊 分析数据: {len(test_df)}条K线")
    result_after = analyzer.analyze_support_resistance(test_df)
    
    if result_after and 'horizontal_lines' in result_after:
        support_lines = result_after['horizontal_lines']['support_lines']
        print(f"✅ 检测到{len(support_lines)}条支撑线:")
        for i, line in enumerate(support_lines):
            print(f"   支撑线{i+1}: 价格={line['price']:.3f}, 时间={line['start_time']}")
        
        # 检查是否还有10.60价位的支撑线
        has_1060_support = any(abs(line['price'] - 10.60) < 0.01 for line in support_lines)
        if has_1060_support:
            print("❌ BUG确认: 10.60支撑线仍然存在（应该被突破移除）")
        else:
            print("✅ 修复成功: 10.60支撑线已被正确移除")
    else:
        print("ℹ️ 未检测到任何支撑线")
    
    # 测试3: 手动验证突破检测逻辑
    print("\n" + "=" * 50)
    print("🔍 测试3: 手动验证突破检测逻辑")
    print("=" * 50)
    
    # 模拟7月24日11:30的swing点
    swing_point = {
        'index': 29,  # 7月24日11:30在新数据中的索引
        'time': pd.to_datetime('2024-07-24 11:30'),
        'price': 10.60,  # 支撑价格（更新为新的最低价）
        'type': 'support'
    }

    print(f"🎯 测试swing点: 价格={swing_point['price']:.3f}, 时间={swing_point['time']}")

    # 使用分析器的突破检测方法
    breakthrough_index = analyzer.find_breakthrough_point(test_df, swing_point)

    if breakthrough_index is not None:
        breakthrough_time = test_df.index[breakthrough_index]
        breakthrough_bar = test_df.iloc[breakthrough_index]
        print(f"🚨 检测到突破: 索引={breakthrough_index}, 时间={breakthrough_time}")
        print(f"   突破K线: 最低价={breakthrough_bar['Low']:.3f} < 支撑价={swing_point['price']:.3f}")
        print("✅ 突破检测逻辑正常工作")
    else:
        print("❌ 未检测到突破（可能存在bug）")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_support_line_breakthrough()
