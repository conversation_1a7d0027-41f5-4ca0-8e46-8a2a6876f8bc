#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试03750实际数据的支撑线突破问题
"""

import pandas as pd
import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from support_and_resistance import analyze_kline_data, create_analyzer

def create_03750_real_data():
    """
    根据您提供的实际DEBUG数据创建测试数据
    """
    # 根据您的DEBUG输出创建实际数据
    test_data = [
        # 前面的数据（简化版，确保有足够的swing检测数据）
        {'time': '2025-07-20 10:30', 'open': 430.0, 'high': 435.0, 'low': 428.0, 'close': 432.0, 'volume': 500000},
        {'time': '2025-07-20 11:30', 'open': 432.0, 'high': 438.0, 'low': 430.0, 'close': 436.0, 'volume': 600000},
        {'time': '2025-07-20 13:30', 'open': 436.0, 'high': 440.0, 'low': 434.0, 'close': 438.0, 'volume': 550000},
        {'time': '2025-07-20 14:30', 'open': 438.0, 'high': 442.0, 'low': 436.0, 'close': 440.0, 'volume': 520000},
        {'time': '2025-07-20 15:30', 'open': 440.0, 'high': 444.0, 'low': 438.0, 'close': 442.0, 'volume': 480000},
        
        {'time': '2025-07-21 10:30', 'open': 442.0, 'high': 446.0, 'low': 440.0, 'close': 444.0, 'volume': 460000},
        {'time': '2025-07-21 11:30', 'open': 444.0, 'high': 448.0, 'low': 442.0, 'close': 446.0, 'volume': 440000},
        {'time': '2025-07-21 13:30', 'open': 446.0, 'high': 450.0, 'low': 444.0, 'close': 448.0, 'volume': 420000},
        {'time': '2025-07-21 14:30', 'open': 448.0, 'high': 452.0, 'low': 446.0, 'close': 450.0, 'volume': 400000},
        {'time': '2025-07-21 15:30', 'open': 450.0, 'high': 454.0, 'low': 448.0, 'close': 452.0, 'volume': 380000},
        
        {'time': '2025-07-22 10:30', 'open': 452.0, 'high': 456.0, 'low': 450.0, 'close': 454.0, 'volume': 360000},
        {'time': '2025-07-22 11:30', 'open': 454.0, 'high': 458.0, 'low': 452.0, 'close': 456.0, 'volume': 340000},
        {'time': '2025-07-22 13:30', 'open': 456.0, 'high': 460.0, 'low': 454.0, 'close': 458.0, 'volume': 320000},
        {'time': '2025-07-22 14:30', 'open': 458.0, 'high': 462.0, 'low': 456.0, 'close': 460.0, 'volume': 300000},
        {'time': '2025-07-22 15:30', 'open': 460.0, 'high': 464.0, 'low': 458.0, 'close': 462.0, 'volume': 280000},
        
        {'time': '2025-07-23 10:30', 'open': 462.0, 'high': 465.0, 'low': 458.0, 'close': 460.0, 'volume': 350000},
        {'time': '2025-07-23 11:30', 'open': 460.0, 'high': 463.0, 'low': 455.0, 'close': 458.0, 'volume': 380000},
        {'time': '2025-07-23 13:30', 'open': 458.0, 'high': 461.0, 'low': 452.0, 'close': 455.0, 'volume': 420000},
        {'time': '2025-07-23 14:30', 'open': 455.0, 'high': 458.0, 'low': 448.0, 'close': 450.0, 'volume': 450000},
        {'time': '2025-07-23 15:30', 'open': 450.0, 'high': 453.0, 'low': 445.0, 'close': 448.0, 'volume': 480000},
        
        # 关键数据：K线278 - swing枢轴点
        {'time': '2025-07-24 11:30', 'open': 428.0, 'high': 428.8, 'low': 422.2, 'close': 426.2, 'volume': 672686},  # 索引20
        {'time': '2025-07-24 13:30', 'open': 426.2, 'high': 430.0, 'low': 425.6, 'close': 426.2, 'volume': 223134},
        {'time': '2025-07-24 14:30', 'open': 426.4, 'high': 428.0, 'low': 425.0, 'close': 426.2, 'volume': 176265},
        {'time': '2025-07-24 15:30', 'open': 426.2, 'high': 427.8, 'low': 425.0, 'close': 427.4, 'volume': 261368},
        {'time': '2025-07-24 16:10', 'open': 427.4, 'high': 428.6, 'low': 427.2, 'close': 428.6, 'volume': 265268},
        
        {'time': '2025-07-25 10:30', 'open': 433.0, 'high': 433.0, 'low': 422.4, 'close': 429.2, 'volume': 505293},
        {'time': '2025-07-25 11:30', 'open': 429.6, 'high': 430.6, 'low': 426.0, 'close': 427.6, 'volume': 239744},
        {'time': '2025-07-25 13:30', 'open': 427.8, 'high': 429.0, 'low': 425.0, 'close': 425.2, 'volume': 125128},
        {'time': '2025-07-25 14:30', 'open': 425.4, 'high': 427.8, 'low': 424.0, 'close': 424.6, 'volume': 110700},
        
        # 关键数据：K线287 - 突破点
        {'time': '2025-07-25 15:30', 'open': 424.8, 'high': 425.8, 'low': 420.0, 'close': 420.4, 'volume': 432826},  # 索引29
    ]
    
    # 转换为DataFrame
    df = pd.DataFrame(test_data)
    
    # 转换列名为支撑阻力算法需要的格式
    df = df.rename(columns={
        'open': 'Open',
        'high': 'High', 
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume'
    })
    
    # 设置时间索引
    df['datetime'] = pd.to_datetime(df['time'])
    df = df.set_index('datetime')
    
    return df

def debug_03750_breakthrough():
    """调试03750实际数据的突破检测"""
    print("=" * 80)
    print("🔍 调试03750实际数据的支撑线突破问题")
    print("=" * 80)
    
    # 创建实际数据
    test_df = create_03750_real_data()
    print(f"📊 实际数据创建完成: {len(test_df)}条60分钟K线")
    print(f"📅 时间范围: {test_df.index[0]} 到 {test_df.index[-1]}")
    
    # 找到关键K线的索引
    k278_time = pd.to_datetime('2025-07-24 11:30')
    k287_time = pd.to_datetime('2025-07-25 15:30')
    
    k278_index = test_df.index.get_loc(k278_time)
    k287_index = test_df.index.get_loc(k287_time)
    
    k278_data = test_df.loc[k278_time]
    k287_data = test_df.loc[k287_time]
    
    print(f"\n📍 关键K线信息:")
    print(f"   K线278 (索引{k278_index}): 时间={k278_time}, 最低价={k278_data['Low']:.1f}")
    print(f"   K线287 (索引{k287_index}): 时间={k287_time}, 最低价={k287_data['Low']:.1f}")
    print(f"   突破确认: {k287_data['Low']:.1f} < {k278_data['Low']:.1f} = {k287_data['Low'] < k278_data['Low']}")
    
    # 创建分析器
    analyzer = create_analyzer(swing_length=8, fvg_range_limit=100)
    
    # 进行支撑阻力分析
    print(f"\n🔍 开始支撑阻力分析...")
    result = analyzer.analyze_support_resistance(test_df)
    
    if result and 'horizontal_lines' in result:
        support_lines = result['horizontal_lines']['support_lines']
        print(f"\n✅ 检测到{len(support_lines)}条支撑线:")
        for i, line in enumerate(support_lines):
            print(f"   支撑线{i+1}: 价格={line['price']:.1f}, 时间={line['start_time']}")
            
        # 检查是否有422.2价位的支撑线
        has_422_support = any(abs(line['price'] - 422.2) < 0.1 for line in support_lines)
        if has_422_support:
            print(f"\n❌ BUG确认: 422.2支撑线仍然存在（应该被420.0突破移除）")
        else:
            print(f"\n✅ 修复成功: 422.2支撑线已被正确移除")
    else:
        print(f"\n❌ 未检测到任何支撑线")
    
    # 手动测试突破检测
    print(f"\n" + "=" * 60)
    print(f"🧪 手动测试突破检测逻辑")
    print(f"=" * 60)
    
    # 模拟K线278的swing点
    swing_point = {
        'index': k278_index,
        'time': k278_time,
        'price': k278_data['Low'],  # 422.2
        'type': 'support'
    }
    
    print(f"🎯 测试swing点: 索引={swing_point['index']}, 价格={swing_point['price']:.1f}, 时间={swing_point['time']}")
    
    # 使用分析器的突破检测方法
    breakthrough_index = analyzer.find_breakthrough_point(test_df, swing_point)
    
    if breakthrough_index is not None:
        breakthrough_time = test_df.index[breakthrough_index]
        breakthrough_bar = test_df.iloc[breakthrough_index]
        print(f"\n🚨 检测到突破:")
        print(f"   突破索引: {breakthrough_index}")
        print(f"   突破时间: {breakthrough_time}")
        print(f"   突破K线最低价: {breakthrough_bar['Low']:.1f}")
        print(f"   支撑价格: {swing_point['price']:.1f}")
        print(f"   突破确认: {breakthrough_bar['Low']:.1f} < {swing_point['price']:.1f} = {breakthrough_bar['Low'] < swing_point['price']}")
        print("✅ 突破检测逻辑正常工作")
    else:
        print(f"\n❌ 未检测到突破 - 这里可能存在bug!")
        print(f"   请检查数据格式和突破检测逻辑")
    
    print(f"\n" + "=" * 80)
    print(f"🏁 调试完成")
    print(f"=" * 80)

if __name__ == "__main__":
    debug_03750_breakthrough()
