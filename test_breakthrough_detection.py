#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试突破检测功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加PySide6目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'PySide6'))

from support_and_resistance import SupportResistanceAnalyzer

def create_test_data_with_breakthrough():
    """创建包含突破的测试数据"""
    # 创建50根K线的测试数据
    dates = pd.date_range(start='2024-01-01', periods=50, freq='15min')
    
    # 创建一个明确的swing high点，然后被突破的情况
    np.random.seed(42)
    base_price = 100.0
    prices = []
    
    for i in range(50):
        if i == 10:  # 创建swing high点
            high = base_price + 5  # 明显的高点
            low = base_price + 2
            open_price = base_price + 2.5
            close = base_price + 4
        elif i in range(5, 10):  # swing high点左侧的低点
            high = base_price + 1
            low = base_price - 1
            open_price = base_price
            close = base_price + 0.5
        elif i in range(11, 16):  # swing high点右侧的低点
            high = base_price + 1
            low = base_price - 1
            open_price = base_price
            close = base_price + 0.5
        elif i == 25:  # 突破swing high点
            high = base_price + 7  # 明显突破之前的高点
            low = base_price + 3
            open_price = base_price + 3
            close = base_price + 6
        else:
            # 正常的随机价格
            volatility = 0.5
            change = np.random.normal(0, volatility)
            high = base_price + abs(change) + 0.2
            low = base_price - abs(change) - 0.2
            open_price = base_price
            close = base_price + change
        
        prices.append({
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close,
            'Volume': np.random.randint(1000, 10000)
        })
        
        base_price = close  # 下一根K线的基准价格
    
    df = pd.DataFrame(prices, index=dates)
    return df

def test_breakthrough_detection():
    """测试突破检测功能"""
    print("=== 测试突破检测功能 ===")
    
    # 创建包含突破的测试数据
    df = create_test_data_with_breakthrough()
    print(f"创建了 {len(df)} 根测试K线")
    
    # 打印关键K线数据
    print(f"\n=== 关键K线数据 ===")
    for i in [9, 10, 11, 24, 25, 26]:
        if i < len(df):
            row = df.iloc[i]
            print(f"K线{i+1:2d}: 时间={df.index[i]}, O={row['Open']:.2f}, H={row['High']:.2f}, L={row['Low']:.2f}, C={row['Close']:.2f}")
    
    # 创建分析器
    analyzer = SupportResistanceAnalyzer(swing_length=5, fvg_range_limit=20)
    
    # 检测swing点
    print(f"\n=== Swing点检测 ===")
    swing_highs, swing_lows = analyzer.find_swing_points(df)
    
    print(f"检测到 {len(swing_highs)} 个swing high点:")
    for i, swing in enumerate(swing_highs):
        print(f"  {i+1}. 索引={swing['index']}, 时间={swing['time']}, 价格={swing['price']:.2f}")
    
    print(f"检测到 {len(swing_lows)} 个swing low点:")
    for i, swing in enumerate(swing_lows):
        print(f"  {i+1}. 索引={swing['index']}, 时间={swing['time']}, 价格={swing['price']:.2f}")
    
    # 检测突破
    print(f"\n=== 突破检测 ===")
    for i, swing in enumerate(swing_highs):
        breakthrough_index = analyzer.find_breakthrough_point(df, swing)
        if breakthrough_index is not None:
            breakthrough_time = df.index[breakthrough_index]
            breakthrough_price = df.iloc[breakthrough_index]['High']
            print(f"Swing High {i+1} 被突破: 索引={breakthrough_index}, 时间={breakthrough_time}, 突破价格={breakthrough_price:.2f}")
        else:
            print(f"Swing High {i+1} 未被突破")
    
    for i, swing in enumerate(swing_lows):
        breakthrough_index = analyzer.find_breakthrough_point(df, swing)
        if breakthrough_index is not None:
            breakthrough_time = df.index[breakthrough_index]
            breakthrough_price = df.iloc[breakthrough_index]['Low']
            print(f"Swing Low {i+1} 被突破: 索引={breakthrough_index}, 时间={breakthrough_time}, 突破价格={breakthrough_price:.2f}")
        else:
            print(f"Swing Low {i+1} 未被突破")
    
    # 创建水平线
    print(f"\n=== 水平线创建 ===")
    resistance_lines = analyzer.create_horizontal_lines(df, swing_highs)
    support_lines = analyzer.create_horizontal_lines(df, swing_lows)
    
    print(f"创建了 {len(resistance_lines)} 条阻力线:")
    for i, line in enumerate(resistance_lines):
        print(f"  {i+1}. 价格={line['price']:.2f}, 开始时间={line['start_time']}")
    
    print(f"创建了 {len(support_lines)} 条支撑线:")
    for i, line in enumerate(support_lines):
        print(f"  {i+1}. 价格={line['price']:.2f}, 开始时间={line['start_time']}")
    
    # 完整分析
    print(f"\n=== 完整分析结果 ===")
    result = analyzer.analyze_support_resistance(df, enable_fvg_filter=False)
    
    print(f"最终结果:")
    print(f"  阻力线: {result['horizontal_lines']['total_resistance_lines']} 条")
    print(f"  支撑线: {result['horizontal_lines']['total_support_lines']} 条")
    
    return result

if __name__ == "__main__":
    try:
        result = test_breakthrough_detection()
        print(f"\n=== 测试完成 ===")
        print("突破检测功能测试完成！")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
